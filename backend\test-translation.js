const { translateTextService } = require("./services/adminService");
require("dotenv").config();

async function testTranslation() {
  try {
    console.log("Testing translation service...");
    console.log("API Key configured:", !!process.env.GOOGLE_AI_API_KEY);

    const testText = "Hello, this is a test message for translation.";
    const targetLang = "fr";

    console.log(`Original text: ${testText}`);
    console.log(`Target language: ${targetLang}`);

    const translatedText = await translateTextService(testText, targetLang);

    console.log(`Translated text: ${translatedText}`);
    console.log("Translation test completed successfully!");

    // Test with different languages
    console.log("\nTesting Spanish translation...");
    const spanishText = await translateTextService(
      "Good morning, how are you?",
      "es"
    );
    console.log(`Spanish translation: ${spanishText}`);
  } catch (error) {
    console.error("Translation test failed:", error.message);
    console.error("Full error:", error);
  }
}

testTranslation();
