const { GoogleGenerativeAI } = require("@google/generative-ai");

exports.translateTextService = async (text, targetLang = "French") => {
  try {
    // Validate input
    if (!text || typeof text !== "string") {
      throw new Error("Invalid text input for translation");
    }

    const API_KEY = process.env.GOOGLE_AI_API_KEY;
    if (!API_KEY) {
      throw new Error("Google AI API key not configured");
    }

    // Determine target language
    const languageMap = {
      fr: "French",
      en: "English",
      es: "Spanish",
      de: "German",
      it: "Italian",
      pt: "Portuguese",
      ar: "Arabic",
    };

    const targetLanguage = languageMap[targetLang] || targetLang || "French";

    // Initialize Google AI
    const genAI = new GoogleGenerativeAI(API_KEY);
    const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" });

    // Create the prompt for translation
    const prompt = `You are a professional translator. Follow these rules strictly:
1. Translate the given text to ${targetLanguage}
2. Maintain exactly the same structure and formatting as the input
3. Keep any special characters, numbers, or technical terms intact
4. Return ONLY the translated text without quotes or additional context
5. If there are bullet points or numbering, preserve them exactly
6. If the text is already in ${targetLanguage}, return it as is

Text to translate: ${text}`;

    // Generate translation
    const result = await model.generateContent(prompt);

    if (!result || !result.response) {
      throw new Error("No response received from Google AI");
    }

    const response = result.response;
    const translatedText = response.text().trim();

    // Validate translated text
    if (!translatedText) {
      throw new Error("Empty translation received from API");
    }

    return translatedText;
  } catch (error) {
    console.error("Error translating text:", error);

    // Return more specific error messages
    if (error.message.includes("API key")) {
      throw new Error("Translation service configuration error");
    } else if (error.message.includes("No response received")) {
      throw new Error("Translation service returned invalid response");
    } else if (error.message.includes("SAFETY")) {
      throw new Error("Translation blocked due to safety filters");
    } else if (error.message.includes("QUOTA_EXCEEDED")) {
      throw new Error("Translation service quota exceeded");
    } else {
      throw new Error(`Translation failed: ${error.message}`);
    }
  }
};
